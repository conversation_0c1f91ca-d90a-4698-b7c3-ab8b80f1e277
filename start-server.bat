@echo off
echo ===================================
echo AI Config Manager - 启动服务器
echo ===================================
echo.

echo 检查 Java 版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Java。请确保安装了 Java 17 或更高版本。
    pause
    exit /b 1
)

echo.
echo 检查 Maven...
mvn -version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Maven。请确保安装了 Maven。
    pause
    exit /b 1
)

echo.
echo 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误: 编译失败。
    pause
    exit /b 1
)

echo.
echo 启动 AI Config Manager 服务器...
echo 请确保:
echo 1. Ollama 服务正在运行 (http://localhost:11434)
echo 2. 已在 application.yml 中配置正确的 Git 仓库路径
echo.
echo 按 Ctrl+C 停止服务器
echo.

mvn spring-boot:run

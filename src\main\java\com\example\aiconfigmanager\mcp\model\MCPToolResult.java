package com.example.aiconfigmanager.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP 工具执行结果
 */
public class MCPToolResult {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("result")
    private Object result;
    
    @JsonProperty("error")
    private String error;
    
    public MCPToolResult() {}
    
    public MCPToolResult(String id, boolean success, Object result, String error) {
        this.id = id;
        this.success = success;
        this.result = result;
        this.error = error;
    }
    
    public static MCPToolResult success(String id, Object result) {
        return new MCPToolResult(id, true, result, null);
    }
    
    public static MCPToolResult error(String id, String error) {
        return new MCPToolResult(id, false, null, error);
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public Object getResult() {
        return result;
    }
    
    public void setResult(Object result) {
        this.result = result;
    }
    
    public String getError() {
        return error;
    }
    
    public void setError(String error) {
        this.error = error;
    }
    
    @Override
    public String toString() {
        return "MCPToolResult{" +
                "id='" + id + '\'' +
                ", success=" + success +
                ", result=" + result +
                ", error='" + error + '\'' +
                '}';
    }
}

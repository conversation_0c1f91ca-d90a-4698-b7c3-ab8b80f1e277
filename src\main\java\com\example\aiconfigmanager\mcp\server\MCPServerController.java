package com.example.aiconfigmanager.mcp.server;

import com.example.aiconfigmanager.mcp.model.MCPPrompt;
import com.example.aiconfigmanager.mcp.model.MCPTool;
import com.example.aiconfigmanager.mcp.model.MCPToolCall;
import com.example.aiconfigmanager.mcp.model.MCPToolResult;
import com.example.aiconfigmanager.mcp.tool.MCPToolExecutor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MCP 服务器控制器
 * 提供工具发现、调用和 SSE 通信功能
 */
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class MCPServerController {
    
    private static final Logger logger = LoggerFactory.getLogger(MCPServerController.class);
    
    private final List<MCPToolExecutor> toolExecutors;
    private final ObjectMapper objectMapper;
    
    public MCPServerController(List<MCPToolExecutor> toolExecutors, ObjectMapper objectMapper) {
        this.toolExecutors = toolExecutors;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 获取所有可用工具
     */
    @GetMapping("/tools")
    public Mono<List<MCPTool>> getTools() {
        logger.info("获取所有可用工具");
        
        List<MCPTool> tools = toolExecutors.stream()
            .map(MCPToolExecutor::getToolDefinition)
            .collect(Collectors.toList());
        
        logger.debug("返回 {} 个工具", tools.size());
        return Mono.just(tools);
    }
    
    /**
     * 获取所有可用提示模板
     */
    @GetMapping("/prompts")
    public Mono<List<MCPPrompt>> getPrompts() {
        logger.info("获取所有可用提示模板");
        
        // 定义一些预设的提示模板
        List<MCPPrompt> prompts = List.of(
            new MCPPrompt(
                "git_branch_helper",
                "Git 分支管理助手，帮助用户进行分支操作",
                List.of(
                    new MCPPrompt.MCPPromptArgument("operation", "要执行的操作描述", true),
                    new MCPPrompt.MCPPromptArgument("branch_info", "分支相关信息", false)
                )
            ),
            new MCPPrompt(
                "code_workflow_assistant",
                "代码工作流助手，协助管理开发流程",
                List.of(
                    new MCPPrompt.MCPPromptArgument("task", "任务描述", true),
                    new MCPPrompt.MCPPromptArgument("context", "上下文信息", false)
                )
            )
        );
        
        return Mono.just(prompts);
    }
    
    /**
     * 执行工具调用
     */
    @PostMapping("/tools/call")
    public Mono<MCPToolResult> callTool(@RequestBody MCPToolCall toolCall) {
        logger.info("执行工具调用: {}", toolCall);
        
        return Mono.fromCallable(() -> {
            MCPToolExecutor executor = toolExecutors.stream()
                .filter(e -> e.getToolName().equals(toolCall.getName()))
                .findFirst()
                .orElse(null);
            
            if (executor == null) {
                return MCPToolResult.error(toolCall.getId(), 
                    "未找到工具: " + toolCall.getName());
            }
            
            return executor.execute(toolCall);
        });
    }
    
    /**
     * SSE 事件流 - 工具发现
     */
    @GetMapping(value = "/events/tools", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> toolDiscoveryEvents() {
        logger.info("开始工具发现事件流");
        
        return Flux.interval(Duration.ofSeconds(1))
            .take(1) // 只发送一次
            .map(i -> {
                try {
                    List<MCPTool> tools = toolExecutors.stream()
                        .map(MCPToolExecutor::getToolDefinition)
                        .collect(Collectors.toList());
                    
                    Map<String, Object> event = Map.of(
                        "type", "tools_discovered",
                        "data", tools,
                        "timestamp", System.currentTimeMillis()
                    );
                    
                    return "data: " + objectMapper.writeValueAsString(event) + "\n\n";
                } catch (Exception e) {
                    logger.error("序列化工具发现事件失败", e);
                    return "data: {\"type\":\"error\",\"message\":\"序列化失败\"}\n\n";
                }
            });
    }
    
    /**
     * SSE 事件流 - 提示模板发现
     */
    @GetMapping(value = "/events/prompts", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> promptDiscoveryEvents() {
        logger.info("开始提示模板发现事件流");
        
        return Flux.interval(Duration.ofSeconds(1))
            .take(1) // 只发送一次
            .map(i -> {
                try {
                    List<MCPPrompt> prompts = List.of(
                        new MCPPrompt(
                            "git_branch_helper",
                            "Git 分支管理助手，帮助用户进行分支操作",
                            List.of(
                                new MCPPrompt.MCPPromptArgument("operation", "要执行的操作描述", true)
                            )
                        )
                    );
                    
                    Map<String, Object> event = Map.of(
                        "type", "prompts_discovered",
                        "data", prompts,
                        "timestamp", System.currentTimeMillis()
                    );
                    
                    return "data: " + objectMapper.writeValueAsString(event) + "\n\n";
                } catch (Exception e) {
                    logger.error("序列化提示发现事件失败", e);
                    return "data: {\"type\":\"error\",\"message\":\"序列化失败\"}\n\n";
                }
            });
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Mono<Map<String, Object>> health() {
        return Mono.just(Map.of(
            "status", "UP",
            "timestamp", System.currentTimeMillis(),
            "tools_count", toolExecutors.size()
        ));
    }
}

package com.example.aiconfigmanager.service;

import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.api.errors.RefAlreadyExistsException;
import org.eclipse.jgit.api.errors.RefNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;

/**
 * Git 分支管理服务
 * 使用 JGit 库执行 Git 操作
 */
@Service
public class GitBranchService {
    
    private static final Logger logger = LoggerFactory.getLogger(GitBranchService.class);
    
    @Value("${mcp.git.repository.path}")
    private String repositoryPath;
    
    /**
     * 创建新分支
     *
     * @param branchName 分支名称
     * @throws GitBranchException 当 Git 操作失败时抛出
     */
    public void createBranch(String branchName) throws GitBranchException {
        logger.info("尝试创建分支: {}", branchName);

        try (Git git = openRepository()) {
            git.branchCreate()
                .setName(branchName)
                .call();

            logger.info("成功创建分支: {}", branchName);

        } catch (RefAlreadyExistsException e) {
            String message = String.format("分支 '%s' 已存在", branchName);
            logger.warn(message);
            throw new GitBranchException(message, e);

        } catch (GitAPIException e) {
            String message = String.format("创建分支 '%s' 失败: %s", branchName, e.getMessage());
            logger.error(message, e);
            throw new GitBranchException(message, e);
        }
    }

    /**
     * 基于指定分支创建新分支
     *
     * @param branchName 新分支名称
     * @param baseBranch 基础分支名称
     * @throws GitBranchException 当 Git 操作失败时抛出
     */
    public void createBranchFromBase(String branchName, String baseBranch) throws GitBranchException {
        logger.info("尝试基于分支 '{}' 创建新分支: {}", baseBranch, branchName);

        try (Git git = openRepository()) {
            // 首先检查基础分支是否存在
            boolean baseBranchExists = git.branchList()
                .call()
                .stream()
                .anyMatch(ref -> ref.getName().endsWith("/" + baseBranch));

            if (!baseBranchExists) {
                throw new GitBranchException(String.format("基础分支 '%s' 不存在", baseBranch));
            }

            git.branchCreate()
                .setName(branchName)
                .setStartPoint("refs/heads/" + baseBranch)
                .call();

            logger.info("成功基于分支 '{}' 创建新分支: {}", baseBranch, branchName);

        } catch (RefAlreadyExistsException e) {
            String message = String.format("分支 '%s' 已存在", branchName);
            logger.warn(message);
            throw new GitBranchException(message, e);

        } catch (GitAPIException e) {
            String message = String.format("基于分支 '%s' 创建分支 '%s' 失败: %s", baseBranch, branchName, e.getMessage());
            logger.error(message, e);
            throw new GitBranchException(message, e);
        }
    }
    
    /**
     * 删除分支
     * 
     * @param branchName 分支名称
     * @throws GitBranchException 当 Git 操作失败时抛出
     */
    public void deleteBranch(String branchName) throws GitBranchException {
        logger.info("尝试删除分支: {}", branchName);
        
        // 防止删除主要分支
        if (isProtectedBranch(branchName)) {
            String message = String.format("无法删除受保护的分支: %s", branchName);
            logger.warn(message);
            throw new GitBranchException(message);
        }
        
        try (Git git = openRepository()) {
            git.branchDelete()
                .setBranchNames(branchName)
                .setForce(true)
                .call();
            
            logger.info("成功删除分支: {}", branchName);
            
        } catch (RefNotFoundException e) {
            String message = String.format("分支 '%s' 不存在", branchName);
            logger.warn(message);
            throw new GitBranchException(message, e);
            
        } catch (GitAPIException e) {
            String message = String.format("删除分支 '%s' 失败: %s", branchName, e.getMessage());
            logger.error(message, e);
            throw new GitBranchException(message, e);
        }
    }
    
    /**
     * 打开 Git 仓库
     * 
     * @return Git 实例
     * @throws GitBranchException 当无法打开仓库时抛出
     */
    private Git openRepository() throws GitBranchException {
        try {
            File repoDir = new File(repositoryPath);
            if (!repoDir.exists()) {
                throw new GitBranchException("Git 仓库路径不存在: " + repositoryPath);
            }
            
            return Git.open(repoDir);
            
        } catch (IOException e) {
            String message = String.format("无法打开 Git 仓库: %s", repositoryPath);
            logger.error(message, e);
            throw new GitBranchException(message, e);
        }
    }
    
    /**
     * 检查是否为受保护的分支
     * 
     * @param branchName 分支名称
     * @return 如果是受保护分支则返回 true
     */
    private boolean isProtectedBranch(String branchName) {
        return "main".equals(branchName) || 
               "master".equals(branchName) || 
               "develop".equals(branchName) ||
               "dev".equals(branchName);
    }
    
    /**
     * Git 分支操作异常
     */
    public static class GitBranchException extends Exception {
        public GitBranchException(String message) {
            super(message);
        }
        
        public GitBranchException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}

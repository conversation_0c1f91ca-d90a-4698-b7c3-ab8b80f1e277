# 快速开始指南

本指南将帮助您在 5 分钟内运行 AI Config Manager。

## 第一步：准备环境

### 1. 安装 Ollama

访问 [https://ollama.ai](https://ollama.ai) 下载并安装 Ollama。

### 2. 下载模型

```bash
# 下载 Qwen 模型（推荐）
ollama pull qwen

# 或者下载 DeepSeek Coder 模型
ollama pull deepseek-coder
```

### 3. 启动 Ollama 服务

```bash
ollama serve
```

确保服务运行在 `http://localhost:11434`

## 第二步：配置项目

### 1. 修改配置文件

编辑 `src/main/resources/application.yml`：

```yaml
mcp:
  git:
    repository:
      path: "E:/your/git/repo/.git"  # 修改为您的实际 Git 仓库路径
```

**示例路径：**
- Windows: `"E:/CODE/project/MyProject/.git"`
- Linux/Mac: `"/home/<USER>/projects/MyProject/.git"`

### 2. 验证 Git 仓库

确保指定的路径是一个有效的 Git 仓库：

```bash
cd /path/to/your/repo
git status  # 应该显示仓库状态
```

## 第三步：启动应用

### 方法一：使用启动脚本（推荐）

**Windows:**
```cmd
start-server.bat
```

**Linux/Mac:**
```bash
chmod +x start-server.sh
./start-server.sh
```

### 方法二：使用 Maven 命令

```bash
mvn spring-boot:run
```

## 第四步：测试应用

### 1. 启动测试客户端

在新的终端窗口中：

**Windows:**
```cmd
start-client.bat
```

**Linux/Mac:**
```bash
chmod +x start-client.sh
./start-client.sh
```

**或者使用 Maven:**
```bash
mvn exec:java -Dexec.mainClass="com.example.aiconfigmanager.client.MCPTestClient"
```

### 2. 测试命令

在客户端中输入以下命令：

```
创建一个名为 feature/user-login 的分支
帮我创建一个用于开发支付功能的分支
删除 test-branch 分支
新建分支 bugfix/issue-123
```

### 3. 预期输出

成功的响应示例：
```json
{
  "commandId": "uuid-here",
  "status": "SUCCESS", 
  "message": "分支 'feature/user-login' 已成功创建"
}
```

## 故障排除

### 问题 1: 连接 Ollama 失败

**错误信息:** `解析自然语言输入失败`

**解决方案:**
1. 确保 Ollama 服务正在运行：`ollama serve`
2. 检查模型是否已下载：`ollama list`
3. 验证 URL 配置：`http://localhost:11434`

### 问题 2: Git 操作失败

**错误信息:** `Git 仓库路径不存在`

**解决方案:**
1. 检查路径是否正确指向 `.git` 目录
2. 确保应用有读写权限
3. 验证是否为有效的 Git 仓库

### 问题 3: 端口冲突

**错误信息:** `Address already in use`

**解决方案:**
修改 `application.yml` 中的端口：
```yaml
mcp:
  server:
    port: 9998  # 改为其他端口
```

## 下一步

- 查看 [README.md](README.md) 了解详细功能
- 探索更多自然语言命令
- 集成到您的开发工具链中

## 支持的命令示例

| 自然语言 | 操作 |
|---------|------|
| "创建分支 feature/login" | 创建分支 |
| "新建一个用于修复bug的分支" | 创建分支 |
| "删除 old-feature 分支" | 删除分支 |
| "移除不需要的测试分支" | 删除分支 |
| "帮我创建开发用户管理功能的分支" | 创建分支 |

AI 会自动解析您的意图并生成合适的分支名称！

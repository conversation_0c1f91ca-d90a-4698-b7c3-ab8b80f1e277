package com.example.aiconfigmanager.client;

import com.example.aiconfigmanager.model.MCPRequest;
import com.example.aiconfigmanager.model.MCPResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.util.Scanner;
import java.util.UUID;

/**
 * MCP 测试客户端
 * 用于测试 MCP 服务器功能的独立应用程序
 */
public class MCPTestClient {
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 9999;
    
    private final ObjectMapper objectMapper;
    
    public MCPTestClient() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 主方法 - 程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        MCPTestClient client = new MCPTestClient();
        client.run();
    }
    
    /**
     * 运行客户端
     */
    public void run() {
        System.out.println("=== MCP 测试客户端 ===");
        System.out.println("连接到服务器: " + SERVER_HOST + ":" + SERVER_PORT);
        System.out.println("输入自然语言指令来管理 Git 分支，输入 'quit' 退出");
        System.out.println("示例指令:");
        System.out.println("  - 创建一个名为 feature/user-login 的分支");
        System.out.println("  - 删除 bugfix/issue-123 分支");
        System.out.println("  - 帮我创建一个用于开发登录功能的分支");
        System.out.println();
        
        try (Socket socket = new Socket(SERVER_HOST, SERVER_PORT);
             BufferedReader serverReader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
             PrintWriter serverWriter = new PrintWriter(socket.getOutputStream(), true);
             Scanner consoleScanner = new Scanner(System.in)) {
            
            System.out.println("已连接到服务器！");
            System.out.println();
            
            while (true) {
                System.out.print("请输入指令: ");
                String userInput = consoleScanner.nextLine().trim();
                
                if ("quit".equalsIgnoreCase(userInput)) {
                    System.out.println("再见！");
                    break;
                }
                
                if (userInput.isEmpty()) {
                    continue;
                }
                
                try {
                    // 发送请求
                    sendRequest(serverWriter, userInput);
                    
                    // 接收响应
                    String responseJson = serverReader.readLine();
                    if (responseJson != null) {
                        processResponse(responseJson);
                    } else {
                        System.out.println("服务器连接已断开");
                        break;
                    }
                    
                } catch (Exception e) {
                    System.err.println("处理请求时发生错误: " + e.getMessage());
                }
                
                System.out.println();
            }
            
        } catch (IOException e) {
            System.err.println("连接服务器失败: " + e.getMessage());
            System.err.println("请确保 MCP 服务器正在运行");
        }
    }
    
    /**
     * 发送请求到服务器
     * 
     * @param writer 服务器写入流
     * @param naturalLanguageInput 自然语言输入
     * @throws IOException 当发送失败时抛出
     */
    private void sendRequest(PrintWriter writer, String naturalLanguageInput) throws IOException {
        String commandId = UUID.randomUUID().toString();
        MCPRequest request = new MCPRequest(commandId, naturalLanguageInput);
        
        String requestJson = objectMapper.writeValueAsString(request);
        System.out.println("发送请求: " + requestJson);
        
        writer.println(requestJson);
    }
    
    /**
     * 处理服务器响应
     * 
     * @param responseJson 响应的 JSON 字符串
     */
    private void processResponse(String responseJson) {
        try {
            System.out.println("收到响应: " + responseJson);
            
            MCPResponse response = objectMapper.readValue(responseJson, MCPResponse.class);
            
            System.out.println("命令 ID: " + response.getCommandId());
            System.out.println("状态: " + response.getStatus());
            System.out.println("消息: " + response.getMessage());
            
            if ("SUCCESS".equals(response.getStatus())) {
                System.out.println("✅ 操作成功！");
            } else {
                System.out.println("❌ 操作失败！");
            }
            
        } catch (Exception e) {
            System.err.println("解析响应失败: " + e.getMessage());
            System.err.println("原始响应: " + responseJson);
        }
    }
}

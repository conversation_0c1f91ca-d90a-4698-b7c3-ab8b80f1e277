package com.example.aiconfigmanager.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP 协议请求消息
 */
public class MCPRequest {
    
    @JsonProperty("commandId")
    private String commandId;
    
    @JsonProperty("naturalLanguageInput")
    private String naturalLanguageInput;
    
    public MCPRequest() {}
    
    public MCPRequest(String commandId, String naturalLanguageInput) {
        this.commandId = commandId;
        this.naturalLanguageInput = naturalLanguageInput;
    }
    
    public String getCommandId() {
        return commandId;
    }
    
    public void setCommandId(String commandId) {
        this.commandId = commandId;
    }
    
    public String getNaturalLanguageInput() {
        return naturalLanguageInput;
    }
    
    public void setNaturalLanguageInput(String naturalLanguageInput) {
        this.naturalLanguageInput = naturalLanguageInput;
    }
    
    @Override
    public String toString() {
        return "MCPRequest{" +
                "commandId='" + commandId + '\'' +
                ", naturalLanguageInput='" + naturalLanguageInput + '\'' +
                '}';
    }
}

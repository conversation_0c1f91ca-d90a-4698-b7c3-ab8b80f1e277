package com.example.aiconfigmanager.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP 协议响应消息
 */
public class MCPResponse {
    
    @JsonProperty("commandId")
    private String commandId;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("message")
    private String message;
    
    public MCPResponse() {}
    
    public MCPResponse(String commandId, String status, String message) {
        this.commandId = commandId;
        this.status = status;
        this.message = message;
    }
    
    public String getCommandId() {
        return commandId;
    }
    
    public void setCommandId(String commandId) {
        this.commandId = commandId;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "MCPResponse{" +
                "commandId='" + commandId + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
    
    /**
     * 创建成功响应
     */
    public static MCPResponse success(String commandId, String message) {
        return new MCPResponse(commandId, "SUCCESS", message);
    }
    
    /**
     * 创建失败响应
     */
    public static MCPResponse failure(String commandId, String message) {
        return new MCPResponse(commandId, "FAILURE", message);
    }
}

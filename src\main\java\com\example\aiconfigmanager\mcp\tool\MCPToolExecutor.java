package com.example.aiconfigmanager.mcp.tool;

import com.example.aiconfigmanager.mcp.model.MCPTool;
import com.example.aiconfigmanager.mcp.model.MCPToolCall;
import com.example.aiconfigmanager.mcp.model.MCPToolResult;

/**
 * MCP 工具执行器接口
 */
public interface MCPToolExecutor {
    
    /**
     * 获取工具定义
     * 
     * @return 工具定义
     */
    MCPTool getToolDefinition();
    
    /**
     * 执行工具
     * 
     * @param toolCall 工具调用请求
     * @return 执行结果
     */
    MCPToolResult execute(MCPToolCall toolCall);
    
    /**
     * 获取工具名称
     * 
     * @return 工具名称
     */
    default String getToolName() {
        return getToolDefinition().getName();
    }
}

# AI 驱动的 Git 分支管理器 (AI Config Manager)

这是一个基于 Spring Boot 的应用程序，使用 AI 技术来管理 Git 分支。它通过自定义的 MCP (My Custom Protocol) 协议接收自然语言指令，使用本地 LLM 解析用户意图，并执行相应的 Git 操作。

## 功能特性

- 🤖 **AI 驱动**: 使用 Spring AI 和 Ollama 解析自然语言指令
- 🌿 **Git 集成**: 使用 JGit 库执行 Git 分支操作
- 🔌 **TCP 服务器**: 通过自定义 MCP 协议提供服务
- 🛡️ **安全保护**: 防止删除受保护的分支（main, master, develop, dev）
- 📝 **详细日志**: 完整的操作日志记录

## 技术栈

- **Java 17**
- **Spring Boot 3.2.x**
- **Spring AI** (Ollama 集成)
- **JGit** (Git 操作)
- **Jackson** (JSON 处理)
- **Maven** (构建工具)

## 前置要求

1. **Java 17** 或更高版本
2. **Maven 3.6+**
3. **Ollama** 服务运行在 `http://localhost:11434`
4. **本地 Git 仓库**

### 安装 Ollama 和模型

```bash
# 安装 Ollama (根据您的操作系统)
# 访问 https://ollama.ai 获取安装说明

# 拉取 Qwen 模型
ollama pull qwen

# 或者拉取 DeepSeek Coder 模型
ollama pull deepseek-coder
```

## 配置

1. 编辑 `src/main/resources/application.yml` 文件
2. 修改 Git 仓库路径：

```yaml
mcp:
  git:
    repository:
      path: "E:/your/git/repo/.git"  # 修改为您的实际 Git 仓库路径（Windows 示例）
      # path: "/home/<USER>/your/git/repo/.git"  # Linux/Mac 示例
```

**重要提示：**
- 路径必须指向 `.git` 目录
- Windows 用户请使用正斜杠 `/` 或双反斜杠 `\\`
- 确保应用有读写该目录的权限

3. 如果需要，可以修改其他配置：

```yaml
mcp:
  server:
    port: 9999  # MCP 服务器端口

spring:
  ai:
    ollama:
      base-url: "http://localhost:11434"  # Ollama 服务地址
      chat:
        model: "qwen"  # 使用的模型名称
```

## 构建和运行

### 构建项目

```bash
mvn clean compile
```

### 运行应用

```bash
mvn spring-boot:run
```

应用启动后，您将看到类似以下的日志：

```
MCP 服务器已启动，监听端口: 9999
```

### 运行测试客户端

在另一个终端中运行测试客户端：

```bash
mvn exec:java -Dexec.mainClass="com.example.aiconfigmanager.client.MCPTestClient"
```

## 使用示例

启动测试客户端后，您可以输入自然语言指令：

### 创建分支示例

```
请输入指令: 创建一个名为 feature/user-login 的分支
请输入指令: 帮我创建一个用于开发登录功能的分支
请输入指令: 新建分支 bugfix/issue-123
```

### 删除分支示例

```
请输入指令: 删除 feature/old-feature 分支
请输入指令: 移除 bugfix/issue-123 分支
请输入指令: 帮我删除不需要的分支 temp/test
```

## MCP 协议格式

### 请求格式

```json
{
  "commandId": "唯一的命令ID",
  "naturalLanguageInput": "创建一个名为'feature/new-login-flow'的分支"
}
```

### 响应格式

```json
{
  "commandId": "唯一的命令ID",
  "status": "SUCCESS",
  "message": "分支 'feature/new-login-flow' 已成功创建。"
}
```

## 项目结构

```
src/main/java/com/example/aiconfigmanager/
├── AiConfigManagerApplication.java     # 主应用类
├── client/
│   └── MCPTestClient.java             # 测试客户端
├── config/
│   └── AIConfiguration.java          # AI 配置
├── mcp/
│   └── MCPServer.java                 # MCP 服务器
├── model/
│   ├── AICommand.java                 # AI 命令模型
│   ├── MCPRequest.java                # MCP 请求模型
│   ├── MCPResponse.java               # MCP 响应模型
│   └── OperationType.java             # 操作类型枚举
└── service/
    ├── AICommandParserService.java    # AI 命令解析服务
    └── GitBranchService.java          # Git 分支服务
```

## 故障排除

### 常见问题

1. **连接 Ollama 失败**
   - 确保 Ollama 服务正在运行
   - 检查 `application.yml` 中的 `base-url` 配置

2. **Git 操作失败**
   - 确保配置的 Git 仓库路径正确
   - 确保应用有读写 Git 仓库的权限

3. **分支已存在错误**
   - 这是正常的保护机制，不会覆盖现有分支

4. **无法删除受保护分支**
   - main, master, develop, dev 分支受到保护，无法删除

### 日志级别

如需更详细的日志，可以在 `application.yml` 中调整日志级别：

```yaml
logging:
  level:
    com.example.aiconfigmanager: DEBUG
    org.springframework.ai: DEBUG
```

## 扩展功能

这个应用可以轻松扩展以支持更多 Git 操作：

- 切换分支
- 合并分支
- 查看分支状态
- 推送到远程仓库

只需在 `OperationType` 枚举中添加新的操作类型，并在相应的服务中实现逻辑即可。

## 许可证

MIT License

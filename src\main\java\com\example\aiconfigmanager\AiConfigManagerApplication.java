package com.example.aiconfigmanager;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * AI 配置管理器主应用类
 * 
 * 这是一个 Spring Boot 应用程序，提供 AI 驱动的 Git 分支管理功能。
 * 应用程序包含以下主要组件：
 * 
 * 1. MCP 服务器 - 监听 TCP 连接，处理客户端请求
 * 2. AI 命令解析服务 - 使用 Spring AI 将自然语言转换为结构化命令
 * 3. Git 分支服务 - 使用 JGit 执行 Git 操作
 * 4. 测试客户端 - 用于测试服务器功能
 * 
 * 使用方法：
 * 1. 确保本地运行 Ollama 服务 (http://localhost:11434)
 * 2. 在 application.yml 中配置正确的 Git 仓库路径
 * 3. 启动应用程序
 * 4. 使用 MCPTestClient 或自定义客户端连接到服务器
 * 5. 发送自然语言指令来管理 Git 分支
 */
@SpringBootApplication
public class AiConfigManagerApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiConfigManagerApplication.class, args);
    }

}

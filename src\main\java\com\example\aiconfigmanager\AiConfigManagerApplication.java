package com.example.aiconfigmanager;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * AI 配置管理器主应用类
 *
 * 这是一个基于 Spring Boot 和 Spring AI 的应用程序，提供 AI 驱动的 Git 分支管理功能。
 * 应用程序包含以下主要组件：
 *
 * 1. MCP 服务器 - 提供工具发现和执行的 RESTful API 和 SSE 事件流
 * 2. MCP 客户端 - 与服务器通信，发现工具并执行调用
 * 3. Spring AI 集成 - 使用 Ollama 本地 LLM 处理自然语言
 * 4. Git 分支服务 - 使用 JGit 执行 Git 操作
 * 5. Web 前端 - 提供用户友好的聊天界面
 *
 * 使用方法：
 * 1. 确保本地运行 Ollama 服务 (http://localhost:11434)
 * 2. 在 application.yml 中配置正确的 Git 仓库路径
 * 3. 启动应用程序
 * 4. 访问 http://localhost:8080 使用 Web 界面
 * 5. 输入自然语言指令来管理 Git 分支
 *
 * 支持的操作：
 * - 创建分支（支持基于指定分支创建）
 * - 删除分支（带安全保护）
 * - 自然语言意图识别
 * - 实时工具发现和调用
 */
@SpringBootApplication
public class AiConfigManagerApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiConfigManagerApplication.class, args);
    }

}

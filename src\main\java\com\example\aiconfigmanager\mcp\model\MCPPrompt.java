package com.example.aiconfigmanager.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * MCP 提示模板定义
 */
public class MCPPrompt {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("arguments")
    private List<MCPPromptArgument> arguments;
    
    public MCPPrompt() {}
    
    public MCPPrompt(String name, String description, List<MCPPromptArgument> arguments) {
        this.name = name;
        this.description = description;
        this.arguments = arguments;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<MCPPromptArgument> getArguments() {
        return arguments;
    }
    
    public void setArguments(List<MCPPromptArgument> arguments) {
        this.arguments = arguments;
    }
    
    /**
     * 提示参数定义
     */
    public static class MCPPromptArgument {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("required")
        private boolean required;
        
        public MCPPromptArgument() {}
        
        public MCPPromptArgument(String name, String description, boolean required) {
            this.name = name;
            this.description = description;
            this.required = required;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public boolean isRequired() {
            return required;
        }
        
        public void setRequired(boolean required) {
            this.required = required;
        }
    }
}

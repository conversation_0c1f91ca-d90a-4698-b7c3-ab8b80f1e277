package com.example.aiconfigmanager.mcp.tool;

import com.example.aiconfigmanager.mcp.model.MCPTool;
import com.example.aiconfigmanager.mcp.model.MCPToolCall;
import com.example.aiconfigmanager.mcp.model.MCPToolResult;
import com.example.aiconfigmanager.service.GitBranchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Git 分支操作工具执行器
 */
@Component
public class GitBranchToolExecutor implements MCPToolExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(GitBranchToolExecutor.class);
    
    private final GitBranchService gitBranchService;
    
    public GitBranchToolExecutor(GitBranchService gitBranchService) {
        this.gitBranchService = gitBranchService;
    }
    
    @Override
    public MCPTool getToolDefinition() {
        MCPTool.MCPToolProperty actionProperty = new MCPTool.MCPToolProperty(
            "string", 
            "操作类型：create（创建分支）或 delete（删除分支）",
            List.of("create", "delete")
        );
        
        MCPTool.MCPToolProperty branchNameProperty = new MCPTool.MCPToolProperty(
            "string", 
            "分支名称，例如：feature/user-login, bugfix/issue-123"
        );
        
        MCPTool.MCPToolProperty baseBranchProperty = new MCPTool.MCPToolProperty(
            "string", 
            "基础分支名称（仅在创建分支时使用），默认为当前分支"
        );
        
        MCPTool.MCPToolSchema schema = new MCPTool.MCPToolSchema(
            Map.of(
                "action", actionProperty,
                "branchName", branchNameProperty,
                "baseBranch", baseBranchProperty
            ),
            List.of("action", "branchName")
        );
        
        return new MCPTool(
            "git_branch_manager",
            "Git 分支管理工具，支持创建和删除分支操作",
            schema
        );
    }
    
    @Override
    public MCPToolResult execute(MCPToolCall toolCall) {
        try {
            Map<String, Object> arguments = toolCall.getArguments();
            String action = (String) arguments.get("action");
            String branchName = (String) arguments.get("branchName");
            String baseBranch = (String) arguments.get("baseBranch");
            
            logger.info("执行 Git 分支操作: action={}, branchName={}, baseBranch={}", 
                       action, branchName, baseBranch);
            
            switch (action.toLowerCase()) {
                case "create":
                    return createBranch(toolCall.getId(), branchName, baseBranch);
                case "delete":
                    return deleteBranch(toolCall.getId(), branchName);
                default:
                    return MCPToolResult.error(toolCall.getId(), 
                        "不支持的操作类型: " + action + "。支持的操作: create, delete");
            }
            
        } catch (Exception e) {
            logger.error("执行 Git 分支工具时发生错误", e);
            return MCPToolResult.error(toolCall.getId(), 
                "执行失败: " + e.getMessage());
        }
    }
    
    private MCPToolResult createBranch(String id, String branchName, String baseBranch) {
        try {
            if (baseBranch != null && !baseBranch.trim().isEmpty()) {
                gitBranchService.createBranchFromBase(branchName, baseBranch);
                return MCPToolResult.success(id, 
                    Map.of(
                        "message", String.format("成功从分支 '%s' 创建新分支 '%s'", baseBranch, branchName),
                        "branchName", branchName,
                        "baseBranch", baseBranch,
                        "action", "create"
                    ));
            } else {
                gitBranchService.createBranch(branchName);
                return MCPToolResult.success(id, 
                    Map.of(
                        "message", String.format("成功创建分支 '%s'", branchName),
                        "branchName", branchName,
                        "action", "create"
                    ));
            }
        } catch (GitBranchService.GitBranchException e) {
            return MCPToolResult.error(id, "创建分支失败: " + e.getMessage());
        }
    }
    
    private MCPToolResult deleteBranch(String id, String branchName) {
        try {
            gitBranchService.deleteBranch(branchName);
            return MCPToolResult.success(id, 
                Map.of(
                    "message", String.format("成功删除分支 '%s'", branchName),
                    "branchName", branchName,
                    "action", "delete"
                ));
        } catch (GitBranchService.GitBranchException e) {
            return MCPToolResult.error(id, "删除分支失败: " + e.getMessage());
        }
    }
}

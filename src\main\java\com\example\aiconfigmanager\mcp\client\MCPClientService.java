package com.example.aiconfigmanager.mcp.client;

import com.example.aiconfigmanager.mcp.model.MCPTool;
import com.example.aiconfigmanager.mcp.model.MCPToolCall;
import com.example.aiconfigmanager.mcp.model.MCPToolResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP 客户端服务
 * 负责与 MCP 服务器通信，发现工具和执行工具调用
 */
@Service
public class MCPClientService {
    
    private static final Logger logger = LoggerFactory.getLogger(MCPClientService.class);
    
    @Value("${mcp.server.base-url}")
    private String mcpServerBaseUrl;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final Map<String, MCPTool> availableTools = new ConcurrentHashMap<>();
    
    public MCPClientService(WebClient.Builder webClientBuilder, ObjectMapper objectMapper) {
        this.webClient = webClientBuilder.build();
        this.objectMapper = objectMapper;
    }
    
    /**
     * 发现可用工具
     */
    public Mono<List<MCPTool>> discoverTools() {
        logger.info("发现 MCP 服务器工具");
        
        return webClient.get()
            .uri(mcpServerBaseUrl + "/tools")
            .retrieve()
            .bodyToMono(new ParameterizedTypeReference<List<MCPTool>>() {})
            .doOnSuccess(tools -> {
                availableTools.clear();
                tools.forEach(tool -> availableTools.put(tool.getName(), tool));
                logger.info("发现 {} 个工具: {}", tools.size(), 
                    tools.stream().map(MCPTool::getName).toList());
            })
            .doOnError(error -> logger.error("发现工具失败", error));
    }
    
    /**
     * 通过 SSE 监听工具发现事件
     */
    public Flux<List<MCPTool>> subscribeToToolDiscovery() {
        logger.info("订阅工具发现事件流");
        
        return webClient.get()
            .uri(mcpServerBaseUrl + "/events/tools")
            .accept(MediaType.TEXT_EVENT_STREAM)
            .retrieve()
            .bodyToFlux(String.class)
            .filter(data -> data.startsWith("data: "))
            .map(data -> data.substring(6)) // 移除 "data: " 前缀
            .filter(data -> !data.trim().isEmpty())
            .flatMap(this::parseToolDiscoveryEvent)
            .doOnNext(tools -> {
                availableTools.clear();
                tools.forEach(tool -> availableTools.put(tool.getName(), tool));
                logger.info("通过 SSE 发现 {} 个工具", tools.size());
            })
            .doOnError(error -> logger.error("SSE 工具发现失败", error));
    }
    
    /**
     * 解析工具发现事件
     */
    private Mono<List<MCPTool>> parseToolDiscoveryEvent(String eventData) {
        try {
            Map<String, Object> event = objectMapper.readValue(eventData, 
                new TypeReference<Map<String, Object>>() {});
            
            if ("tools_discovered".equals(event.get("type"))) {
                @SuppressWarnings("unchecked")
                List<Object> toolsData = (List<Object>) event.get("data");
                
                List<MCPTool> tools = toolsData.stream()
                    .map(toolData -> objectMapper.convertValue(toolData, MCPTool.class))
                    .toList();
                
                return Mono.just(tools);
            }
            
            return Mono.empty();
        } catch (Exception e) {
            logger.error("解析工具发现事件失败: {}", eventData, e);
            return Mono.empty();
        }
    }
    
    /**
     * 调用工具
     */
    public Mono<MCPToolResult> callTool(String toolName, Map<String, Object> arguments) {
        logger.info("调用工具: {} with arguments: {}", toolName, arguments);
        
        if (!availableTools.containsKey(toolName)) {
            return Mono.just(MCPToolResult.error(UUID.randomUUID().toString(), 
                "工具不存在: " + toolName));
        }
        
        MCPToolCall toolCall = new MCPToolCall(
            UUID.randomUUID().toString(),
            toolName,
            arguments
        );
        
        return webClient.post()
            .uri(mcpServerBaseUrl + "/tools/call")
            .bodyValue(toolCall)
            .retrieve()
            .bodyToMono(MCPToolResult.class)
            .doOnSuccess(result -> logger.info("工具调用结果: {}", result))
            .doOnError(error -> logger.error("工具调用失败", error));
    }
    
    /**
     * 获取可用工具列表
     */
    public List<MCPTool> getAvailableTools() {
        return List.copyOf(availableTools.values());
    }
    
    /**
     * 检查工具是否可用
     */
    public boolean isToolAvailable(String toolName) {
        return availableTools.containsKey(toolName);
    }
    
    /**
     * 获取工具定义
     */
    public MCPTool getToolDefinition(String toolName) {
        return availableTools.get(toolName);
    }
    
    /**
     * 健康检查
     */
    public Mono<Boolean> healthCheck() {
        return webClient.get()
            .uri(mcpServerBaseUrl + "/health")
            .retrieve()
            .bodyToMono(Map.class)
            .map(response -> "UP".equals(response.get("status")))
            .doOnSuccess(healthy -> logger.debug("MCP 服务器健康状态: {}", healthy))
            .onErrorReturn(false);
    }
}

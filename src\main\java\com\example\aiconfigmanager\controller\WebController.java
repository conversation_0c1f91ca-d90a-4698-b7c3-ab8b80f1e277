package com.example.aiconfigmanager.controller;

import com.example.aiconfigmanager.service.AICommandParserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Web 控制器
 * 处理前端页面和 API 请求
 */
@Controller
public class WebController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebController.class);
    
    private final AICommandParserService aiCommandParserService;
    
    public WebController(AICommandParserService aiCommandParserService) {
        this.aiCommandParserService = aiCommandParserService;
    }
    
    /**
     * 主页
     */
    @GetMapping("/")
    public String index() {
        return "index";
    }
    
    /**
     * 聊天 API
     */
    @PostMapping("/api/chat")
    @ResponseBody
    public Mono<String> chat(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        logger.info("收到聊天请求: {}", message);
        
        if (message == null || message.trim().isEmpty()) {
            return Mono.just("请输入有效的指令。");
        }
        
        return aiCommandParserService.processUserRequest(message.trim())
            .doOnSuccess(response -> logger.info("聊天响应: {}", response))
            .doOnError(error -> logger.error("聊天处理失败", error));
    }
}

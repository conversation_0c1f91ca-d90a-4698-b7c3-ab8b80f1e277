package com.example.aiconfigmanager.mcp;

import com.example.aiconfigmanager.model.AICommand;
import com.example.aiconfigmanager.model.MCPRequest;
import com.example.aiconfigmanager.model.MCPResponse;
import com.example.aiconfigmanager.model.OperationType;
import com.example.aiconfigmanager.service.AICommandParserService;
import com.example.aiconfigmanager.service.GitBranchService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MCP (My Custom Protocol) 服务器
 * 监听 TCP 连接并处理客户端请求
 */
@Component
public class MCPServer {
    
    private static final Logger logger = LoggerFactory.getLogger(MCPServer.class);
    
    @Value("${mcp.server.port}")
    private int serverPort;
    
    private final AICommandParserService aiCommandParserService;
    private final GitBranchService gitBranchService;
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService;
    
    private ServerSocket serverSocket;
    private volatile boolean running = false;
    
    public MCPServer(AICommandParserService aiCommandParserService, 
                     GitBranchService gitBranchService) {
        this.aiCommandParserService = aiCommandParserService;
        this.gitBranchService = gitBranchService;
        this.objectMapper = new ObjectMapper();
        this.executorService = Executors.newCachedThreadPool();
    }
    
    /**
     * 启动 MCP 服务器
     */
    @PostConstruct
    public void startServer() {
        executorService.submit(() -> {
            try {
                serverSocket = new ServerSocket(serverPort);
                running = true;
                logger.info("MCP 服务器已启动，监听端口: {}", serverPort);
                
                while (running && !Thread.currentThread().isInterrupted()) {
                    try {
                        Socket clientSocket = serverSocket.accept();
                        logger.info("接受新的客户端连接: {}", clientSocket.getRemoteSocketAddress());
                        
                        // 为每个客户端创建新的处理线程
                        executorService.submit(new ClientHandler(clientSocket));
                        
                    } catch (IOException e) {
                        if (running) {
                            logger.error("接受客户端连接时发生错误", e);
                        }
                    }
                }
                
            } catch (IOException e) {
                logger.error("启动 MCP 服务器失败", e);
            }
        });
    }
    
    /**
     * 停止 MCP 服务器
     */
    @PreDestroy
    public void stopServer() {
        running = false;
        
        if (serverSocket != null && !serverSocket.isClosed()) {
            try {
                serverSocket.close();
                logger.info("MCP 服务器已停止");
            } catch (IOException e) {
                logger.error("关闭服务器套接字时发生错误", e);
            }
        }
        
        executorService.shutdown();
    }
    
    /**
     * 客户端处理器
     */
    private class ClientHandler implements Runnable {
        private final Socket clientSocket;
        
        public ClientHandler(Socket clientSocket) {
            this.clientSocket = clientSocket;
        }
        
        @Override
        public void run() {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                 PrintWriter writer = new PrintWriter(clientSocket.getOutputStream(), true)) {
                
                String requestLine;
                while ((requestLine = reader.readLine()) != null) {
                    logger.debug("收到客户端请求: {}", requestLine);
                    
                    MCPResponse response = processRequest(requestLine);
                    String responseJson = objectMapper.writeValueAsString(response);
                    
                    writer.println(responseJson);
                    logger.debug("发送响应: {}", responseJson);
                }
                
            } catch (IOException e) {
                logger.error("处理客户端连接时发生错误", e);
            } finally {
                try {
                    clientSocket.close();
                    logger.info("客户端连接已关闭: {}", clientSocket.getRemoteSocketAddress());
                } catch (IOException e) {
                    logger.error("关闭客户端套接字时发生错误", e);
                }
            }
        }
        
        /**
         * 处理客户端请求
         * 
         * @param requestJson 请求的 JSON 字符串
         * @return MCP 响应
         */
        private MCPResponse processRequest(String requestJson) {
            try {
                // 解析请求
                MCPRequest request = objectMapper.readValue(requestJson, MCPRequest.class);
                logger.info("处理请求: {}", request);
                
                // 使用 AI 解析自然语言输入
                AICommand command = aiCommandParserService.parse(request.getNaturalLanguageInput());
                
                if (!command.isValid()) {
                    return MCPResponse.failure(request.getCommandId(), 
                        "无法理解您的指令，请提供更明确的分支操作描述");
                }
                
                // 执行 Git 操作
                return executeGitOperation(request.getCommandId(), command);
                
            } catch (Exception e) {
                logger.error("处理请求时发生错误", e);
                return MCPResponse.failure("unknown", "处理请求时发生内部错误: " + e.getMessage());
            }
        }
        
        /**
         * 执行 Git 操作
         * 
         * @param commandId 命令 ID
         * @param command AI 解析的命令
         * @return MCP 响应
         */
        private MCPResponse executeGitOperation(String commandId, AICommand command) {
            try {
                OperationType operation = command.getOperationType();
                String branchName = command.branchName();
                
                switch (operation) {
                    case CREATE_BRANCH:
                        gitBranchService.createBranch(branchName);
                        return MCPResponse.success(commandId, 
                            String.format("分支 '%s' 已成功创建", branchName));
                        
                    case DELETE_BRANCH:
                        gitBranchService.deleteBranch(branchName);
                        return MCPResponse.success(commandId, 
                            String.format("分支 '%s' 已成功删除", branchName));
                        
                    default:
                        return MCPResponse.failure(commandId, 
                            "不支持的操作类型: " + operation);
                }
                
            } catch (GitBranchService.GitBranchException e) {
                return MCPResponse.failure(commandId, e.getMessage());
            } catch (Exception e) {
                logger.error("执行 Git 操作时发生错误", e);
                return MCPResponse.failure(commandId, "执行 Git 操作时发生错误: " + e.getMessage());
            }
        }
    }
}

<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Git 分支管理器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            max-width: 80%;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .ai-message {
            background-color: white;
            border: 1px solid #dee2e6;
            margin-right: auto;
        }
        .loading {
            display: none;
        }
        .tools-panel {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .tool-item {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 - 工具面板 -->
            <div class="col-md-3">
                <div class="tools-panel">
                    <h5><i class="fas fa-tools"></i> 可用工具</h5>
                    <div class="mb-2">
                        <span class="status-indicator" id="connectionStatus"></span>
                        <small id="connectionText">连接中...</small>
                    </div>
                    <div id="toolsList">
                        <div class="text-muted">正在加载工具...</div>
                    </div>
                </div>
                
                <div class="tools-panel">
                    <h6><i class="fas fa-lightbulb"></i> 使用示例</h6>
                    <div class="small text-muted">
                        <div class="mb-2">• 创建一个名为 feature/user-login 的分支</div>
                        <div class="mb-2">• 帮我基于 main 分支创建一个 feature/payment 分支</div>
                        <div class="mb-2">• 删除 old-feature 分支</div>
                        <div class="mb-2">• 新建一个用于修复bug的分支</div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-robot"></i> AI Git 分支管理器</h1>
                    <button class="btn btn-outline-secondary" onclick="clearChat()">
                        <i class="fas fa-trash"></i> 清空对话
                    </button>
                </div>
                
                <!-- 聊天区域 -->
                <div class="chat-container" id="chatContainer">
                    <div class="ai-message message">
                        <strong><i class="fas fa-robot"></i> AI 助手</strong><br>
                        您好！我是您的 Git 分支管理助手。您可以用自然语言告诉我您想要执行的 Git 操作，比如创建或删除分支。
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="mt-3">
                    <div class="input-group">
                        <input type="text" class="form-control" id="userInput" 
                               placeholder="请输入您的指令，例如：创建一个名为 feature/login 的分支"
                               onkeypress="handleKeyPress(event)">
                        <button class="btn btn-primary" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                    </div>
                    <div class="loading mt-2" id="loadingIndicator">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">处理中...</span>
                        </div>
                        <span class="ms-2">AI 正在处理您的请求...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let isConnected = false;
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTools();
            setupSSEConnection();
        });
        
        // 加载可用工具
        async function loadTools() {
            try {
                const response = await fetch('/mcp/tools');
                const tools = await response.json();
                displayTools(tools);
                updateConnectionStatus(true);
            } catch (error) {
                console.error('加载工具失败:', error);
                updateConnectionStatus(false);
            }
        }
        
        // 显示工具列表
        function displayTools(tools) {
            const toolsList = document.getElementById('toolsList');
            if (tools.length === 0) {
                toolsList.innerHTML = '<div class="text-muted">暂无可用工具</div>';
                return;
            }
            
            toolsList.innerHTML = tools.map(tool => `
                <div class="tool-item">
                    <strong>${tool.name}</strong><br>
                    <small class="text-muted">${tool.description}</small>
                </div>
            `).join('');
        }
        
        // 设置 SSE 连接
        function setupSSEConnection() {
            const eventSource = new EventSource('/mcp/events/tools');
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'tools_discovered') {
                        displayTools(data.data);
                        updateConnectionStatus(true);
                    }
                } catch (error) {
                    console.error('解析 SSE 数据失败:', error);
                }
            };
            
            eventSource.onerror = function(error) {
                console.error('SSE 连接错误:', error);
                updateConnectionStatus(false);
            };
        }
        
        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = '已连接';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '连接断开';
            }
        }
        
        // 处理键盘事件
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage(message, 'user');
            input.value = '';
            
            // 显示加载指示器
            showLoading(true);
            
            try {
                // 发送请求到后端
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const result = await response.text();
                
                // 显示 AI 响应
                addMessage(result, 'ai');
                
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('抱歉，处理您的请求时发生错误。请稍后重试。', 'ai');
            } finally {
                showLoading(false);
            }
        }
        
        // 添加消息到聊天区域
        function addMessage(content, type) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            
            if (type === 'user') {
                messageDiv.innerHTML = `<strong><i class="fas fa-user"></i> 您</strong><br>${content}`;
            } else {
                messageDiv.innerHTML = `<strong><i class="fas fa-robot"></i> AI 助手</strong><br>${content}`;
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 显示/隐藏加载指示器
        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.style.display = show ? 'block' : 'none';
        }
        
        // 清空对话
        function clearChat() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = `
                <div class="ai-message message">
                    <strong><i class="fas fa-robot"></i> AI 助手</strong><br>
                    您好！我是您的 Git 分支管理助手。您可以用自然语言告诉我您想要执行的 Git 操作，比如创建或删除分支。
                </div>
            `;
        }
    </script>
</body>
</html>

server:
  port: 8080 # 标准 Spring Boot 端口，但我们的自定义 TCP 服务器将使用另一个端口

mcp:
  server:
    port: 9999 # 我们自定义的 MCP 服务器端口
  git:
    repository:
      path: "/path/to/your/local/git/repo/.git" # 重要：请使用一个占位符路径，我稍后会修改它

spring:
  ai:
    ollama:
      base-url: "http://localhost:11434" # 默认的 Ollama URL。我们会将其指向 DeepSeek/Qwen
      chat:
        model: "qwen" # 默认使用的模型。可以是 "deepseek-coder", "qwen" 等
        options:
          temperature: 0.1

logging:
  level:
    com.example.aiconfigmanager: DEBUG
    org.springframework.ai: DEBUG

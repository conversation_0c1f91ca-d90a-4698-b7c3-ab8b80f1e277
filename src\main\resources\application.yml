server:
  port: 8080

mcp:
  server:
    base-url: "http://localhost:8080/mcp" # MCP 服务器基础 URL
  git:
    repository:
      path: "/path/to/your/local/git/repo/.git" # Git 仓库路径
  tools:
    enabled: true # 启用工具功能

spring:
  ai:
    ollama:
      base-url: "http://localhost:11434"
      chat:
        model: "qwen"
        options:
          temperature: 0.1
          max-tokens: 2000
  thymeleaf:
    cache: false # 开发时禁用缓存

logging:
  level:
    com.example.aiconfigmanager: DEBUG
    org.springframework.ai: DEBUG
    org.springframework.web.reactive: DEBUG

package com.example.aiconfigmanager.model;

/**
 * 表示从 AI 解析出的结构化命令
 * 
 * @param operation 操作类型（CREATE_BRANCH, DELETE_BRANCH, UNKNOWN）
 * @param branchName 分支名称
 * @param originalInput 原始的自然语言输入
 */
public record AICommand(String operation, String branchName, String originalInput) {
    
    /**
     * 检查命令是否有效
     * 
     * @return 如果操作不是 UNKNOWN 且分支名不为空，则返回 true
     */
    public boolean isValid() {
        return operation != null && 
               !OperationType.UNKNOWN.name().equals(operation) && 
               branchName != null && 
               !branchName.trim().isEmpty();
    }
    
    /**
     * 获取操作类型枚举
     * 
     * @return OperationType 枚举值
     */
    public OperationType getOperationType() {
        try {
            return OperationType.valueOf(operation);
        } catch (IllegalArgumentException e) {
            return OperationType.UNKNOWN;
        }
    }
}

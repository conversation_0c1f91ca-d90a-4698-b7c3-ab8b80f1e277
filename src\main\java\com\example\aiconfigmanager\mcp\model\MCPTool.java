package com.example.aiconfigmanager.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * MCP 工具定义
 */
public class MCPTool {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("inputSchema")
    private MCPToolSchema inputSchema;
    
    public MCPTool() {}
    
    public MCPTool(String name, String description, MCPToolSchema inputSchema) {
        this.name = name;
        this.description = description;
        this.inputSchema = inputSchema;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public MCPToolSchema getInputSchema() {
        return inputSchema;
    }
    
    public void setInputSchema(MCPToolSchema inputSchema) {
        this.inputSchema = inputSchema;
    }
    
    /**
     * 工具参数模式定义
     */
    public static class MCPToolSchema {
        @JsonProperty("type")
        private String type = "object";
        
        @JsonProperty("properties")
        private Map<String, MCPToolProperty> properties;
        
        @JsonProperty("required")
        private List<String> required;
        
        public MCPToolSchema() {}
        
        public MCPToolSchema(Map<String, MCPToolProperty> properties, List<String> required) {
            this.properties = properties;
            this.required = required;
        }
        
        // Getters and Setters
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public Map<String, MCPToolProperty> getProperties() {
            return properties;
        }
        
        public void setProperties(Map<String, MCPToolProperty> properties) {
            this.properties = properties;
        }
        
        public List<String> getRequired() {
            return required;
        }
        
        public void setRequired(List<String> required) {
            this.required = required;
        }
    }
    
    /**
     * 工具属性定义
     */
    public static class MCPToolProperty {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("enum")
        private List<String> enumValues;
        
        public MCPToolProperty() {}
        
        public MCPToolProperty(String type, String description) {
            this.type = type;
            this.description = description;
        }
        
        public MCPToolProperty(String type, String description, List<String> enumValues) {
            this.type = type;
            this.description = description;
            this.enumValues = enumValues;
        }
        
        // Getters and Setters
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public List<String> getEnumValues() {
            return enumValues;
        }
        
        public void setEnumValues(List<String> enumValues) {
            this.enumValues = enumValues;
        }
    }
}

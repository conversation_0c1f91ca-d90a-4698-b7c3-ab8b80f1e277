package com.example.aiconfigmanager.service;

import com.example.aiconfigmanager.model.AICommand;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * AI 命令解析服务
 * 使用 Ollama API 将自然语言转换为结构化命令
 */
@Service
public class AICommandParserService {

    private static final Logger logger = LoggerFactory.getLogger(AICommandParserService.class);

    @Value("${spring.ai.ollama.base-url:http://localhost:11434}")
    private String ollamaBaseUrl;

    @Value("${spring.ai.ollama.chat.model:qwen}")
    private String modelName;

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    private static final String SYSTEM_PROMPT = """
        你是一个 Git 管理系统的指令解析器。你唯一的任务是分析用户文本并判断其意图。
        可能的意图是 'CREATE_BRANCH' 或 'DELETE_BRANCH'。
        你必须提取出分支名称。分支名应遵循标准的 Git 约定（例如 'feature/user-auth', 'bugfix/issue-123'）。
        如果你无法确定意图或分支名，请将操作分类为 'UNKNOWN'。
        你必须只返回一个 JSON 对象，不包含任何其他文本或解释。JSON 结构应如下：
        {
          "operation": "CREATE_BRANCH" 或 "DELETE_BRANCH" 或 "UNKNOWN",
          "branchName": "提取出的分支名" 或 null
        }
        """;
    
    public AICommandParserService() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 解析自然语言输入为结构化命令
     *
     * @param naturalLanguageInput 自然语言输入
     * @return 解析后的 AICommand 对象
     * @throws AIParsingException 当解析失败时抛出
     */
    public AICommand parse(String naturalLanguageInput) throws AIParsingException {
        logger.info("开始解析自然语言输入: {}", naturalLanguageInput);

        try {
            // 构建 Ollama API 请求
            Map<String, Object> requestBody = Map.of(
                "model", modelName,
                "prompt", SYSTEM_PROMPT + "\n\n用户输入: " + naturalLanguageInput,
                "stream", false,
                "options", Map.of("temperature", 0.1)
            );

            // 调用 Ollama API
            Mono<Map> responseMono = webClient.post()
                .uri(ollamaBaseUrl + "/api/generate")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(Map.class);

            Map<String, Object> response = responseMono.block();
            if (response == null || !response.containsKey("response")) {
                throw new AIParsingException("Ollama API 返回空响应");
            }

            String aiResponse = (String) response.get("response");
            logger.debug("AI 响应: {}", aiResponse);

            // 解析 JSON 响应
            AICommand command = parseAIResponse(aiResponse, naturalLanguageInput);

            logger.info("解析完成: {}", command);
            return command;

        } catch (Exception e) {
            String message = String.format("解析自然语言输入失败: %s", e.getMessage());
            logger.error(message, e);
            throw new AIParsingException(message, e);
        }
    }
    
    /**
     * 解析 AI 的 JSON 响应
     * 
     * @param aiResponse AI 的响应文本
     * @param originalInput 原始输入
     * @return AICommand 对象
     * @throws AIParsingException 当 JSON 解析失败时抛出
     */
    private AICommand parseAIResponse(String aiResponse, String originalInput) throws AIParsingException {
        try {
            // 清理响应文本，移除可能的 markdown 代码块标记
            String cleanedResponse = aiResponse.trim();
            if (cleanedResponse.startsWith("```json")) {
                cleanedResponse = cleanedResponse.substring(7);
            }
            if (cleanedResponse.endsWith("```")) {
                cleanedResponse = cleanedResponse.substring(0, cleanedResponse.length() - 3);
            }
            cleanedResponse = cleanedResponse.trim();
            
            // 解析 JSON
            JsonNode jsonNode = objectMapper.readTree(cleanedResponse);
            
            String operation = jsonNode.has("operation") ? jsonNode.get("operation").asText() : "UNKNOWN";
            String branchName = jsonNode.has("branchName") && !jsonNode.get("branchName").isNull() 
                ? jsonNode.get("branchName").asText() : null;
            
            return new AICommand(operation, branchName, originalInput);
            
        } catch (JsonProcessingException e) {
            String message = String.format("无法解析 AI 响应为 JSON: %s", aiResponse);
            logger.error(message, e);
            throw new AIParsingException(message, e);
        }
    }
    
    /**
     * AI 解析异常
     */
    public static class AIParsingException extends Exception {
        public AIParsingException(String message) {
            super(message);
        }
        
        public AIParsingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}

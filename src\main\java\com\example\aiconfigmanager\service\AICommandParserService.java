package com.example.aiconfigmanager.service;

import com.example.aiconfigmanager.mcp.client.MCPClientService;
import com.example.aiconfigmanager.mcp.model.MCPTool;
import com.example.aiconfigmanager.mcp.model.MCPToolResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.function.FunctionCallbackWrapper;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * AI 命令解析和执行服务
 * 使用 Spring AI 和 MCP 工具来处理用户请求
 */
@Service
public class AICommandParserService {

    private static final Logger logger = LoggerFactory.getLogger(AICommandParserService.class);

    private final ChatClient chatClient;
    private final MCPClientService mcpClientService;
    private final ObjectMapper objectMapper;
    
    public AICommandParserService(ChatClient chatClient, MCPClientService mcpClientService) {
        this.chatClient = chatClient;
        this.mcpClientService = mcpClientService;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 处理用户请求，使用 AI 和 MCP 工具
     *
     * @param userInput 用户输入
     * @return 处理结果
     */
    public Mono<String> processUserRequest(String userInput) {
        logger.info("处理用户请求: {}", userInput);

        return mcpClientService.discoverTools()
            .flatMap(tools -> {
                if (tools.isEmpty()) {
                    return Mono.just("抱歉，当前没有可用的工具来处理您的请求。");
                }

                // 构建系统提示，包含可用工具信息
                String systemPrompt = buildSystemPrompt(tools);

                // 调用 AI 模型
                return callAIWithTools(systemPrompt, userInput, tools);
            })
            .onErrorResume(error -> {
                logger.error("处理用户请求失败", error);
                return Mono.just("处理请求时发生错误: " + error.getMessage());
            });
    }

    /**
     * 构建包含工具信息的系统提示
     */
    private String buildSystemPrompt(List<MCPTool> tools) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个智能的 Git 分支管理助手。你可以使用以下工具来帮助用户：\n\n");

        for (MCPTool tool : tools) {
            prompt.append("工具名称: ").append(tool.getName()).append("\n");
            prompt.append("描述: ").append(tool.getDescription()).append("\n");
            prompt.append("参数: ").append(tool.getInputSchema().getProperties().keySet()).append("\n\n");
        }

        prompt.append("当用户请求执行 Git 操作时，请分析用户意图并调用相应的工具。\n");
        prompt.append("如果用户提到基于某个分支创建新分支，请使用 baseBranch 参数。\n");
        prompt.append("请用中文回复用户。");

        return prompt.toString();
    }

    /**
     * 调用 AI 模型并处理工具调用
     */
    private Mono<String> callAIWithTools(String systemPrompt, String userInput, List<MCPTool> tools) {
        try {
            // 使用 Spring AI 的 ChatClient
            String response = chatClient.prompt()
                .system(systemPrompt)
                .user(userInput)
                .call()
                .content();

            logger.debug("AI 响应: {}", response);

            // 检查是否需要调用工具
            return parseAndExecuteToolCalls(response, userInput);

        } catch (Exception e) {
            logger.error("调用 AI 模型失败", e);
            return Mono.just("AI 处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析 AI 响应并执行工具调用
     */
    private Mono<String> parseAndExecuteToolCalls(String aiResponse, String userInput) {
        // 简单的意图识别和工具调用
        if (containsCreateBranchIntent(userInput)) {
            return handleCreateBranchRequest(userInput);
        } else if (containsDeleteBranchIntent(userInput)) {
            return handleDeleteBranchRequest(userInput);
        } else {
            return Mono.just(aiResponse);
        }
    }

    /**
     * 处理创建分支请求
     */
    private Mono<String> handleCreateBranchRequest(String userInput) {
        Map<String, Object> arguments = extractBranchArguments(userInput, "create");

        return mcpClientService.callTool("git_branch_manager", arguments)
            .map(result -> {
                if (result.isSuccess()) {
                    return "✅ " + ((Map<?, ?>) result.getResult()).get("message");
                } else {
                    return "❌ " + result.getError();
                }
            });
    }

    /**
     * 处理删除分支请求
     */
    private Mono<String> handleDeleteBranchRequest(String userInput) {
        Map<String, Object> arguments = extractBranchArguments(userInput, "delete");

        return mcpClientService.callTool("git_branch_manager", arguments)
            .map(result -> {
                if (result.isSuccess()) {
                    return "✅ " + ((Map<?, ?>) result.getResult()).get("message");
                } else {
                    return "❌ " + result.getError();
                }
            });
    }
    
    /**
     * 检查是否包含创建分支意图
     */
    private boolean containsCreateBranchIntent(String input) {
        String lowerInput = input.toLowerCase();
        return lowerInput.contains("创建") || lowerInput.contains("新建") ||
               lowerInput.contains("create") || lowerInput.contains("新增");
    }

    /**
     * 检查是否包含删除分支意图
     */
    private boolean containsDeleteBranchIntent(String input) {
        String lowerInput = input.toLowerCase();
        return lowerInput.contains("删除") || lowerInput.contains("移除") ||
               lowerInput.contains("delete") || lowerInput.contains("remove");
    }

    /**
     * 提取分支操作参数
     */
    private Map<String, Object> extractBranchArguments(String input, String action) {
        Map<String, Object> arguments = Map.of("action", action);

        // 简单的分支名提取逻辑
        String branchName = extractBranchName(input);
        if (branchName != null) {
            arguments = Map.of(
                "action", action,
                "branchName", branchName
            );

            // 检查是否有基础分支
            String baseBranch = extractBaseBranch(input);
            if (baseBranch != null) {
                arguments = Map.of(
                    "action", action,
                    "branchName", branchName,
                    "baseBranch", baseBranch
                );
            }
        }

        return arguments;
    }

    /**
     * 提取分支名称
     */
    private String extractBranchName(String input) {
        // 查找常见的分支名模式
        String[] patterns = {
            "feature/[\\w-]+",
            "bugfix/[\\w-]+",
            "hotfix/[\\w-]+",
            "[\\w-]+/[\\w-]+",
            "[\\w-]+"
        };

        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(input);
            if (m.find()) {
                return m.group();
            }
        }

        // 如果没有找到模式，尝试从引号中提取
        java.util.regex.Pattern quotedPattern = java.util.regex.Pattern.compile("['\"]([^'\"]+)['\"]");
        java.util.regex.Matcher quotedMatcher = quotedPattern.matcher(input);
        if (quotedMatcher.find()) {
            return quotedMatcher.group(1);
        }

        return null;
    }

    /**
     * 提取基础分支名称
     */
    private String extractBaseBranch(String input) {
        // 查找 "基于xxx" 或 "from xxx" 模式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(?:基于|from)\\s*([\\w/-]+)");
        java.util.regex.Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }
}
}

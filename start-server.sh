#!/bin/bash

echo "==================================="
echo "AI Config Manager - 启动服务器"
echo "==================================="
echo

echo "检查 Java 版本..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到 Java。请确保安装了 Java 17 或更高版本。"
    exit 1
fi
java -version

echo
echo "检查 Maven..."
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到 Maven。请确保安装了 Maven。"
    exit 1
fi
mvn -version

echo
echo "编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "错误: 编译失败。"
    exit 1
fi

echo
echo "启动 AI Config Manager 服务器..."
echo "请确保:"
echo "1. Ollama 服务正在运行 (http://localhost:11434)"
echo "2. 已在 application.yml 中配置正确的 Git 仓库路径"
echo
echo "按 Ctrl+C 停止服务器"
echo

mvn spring-boot:run
